#!/usr/bin/env python3
"""
搜索IP地址***************的二进制表示
"""

import struct

# IP地址 ***************
ip_parts = [101, 126, 151, 252]
print(f"搜索IP地址: {'.'.join(map(str, ip_parts))}")
print("=" * 50)

# 不同的二进制表示方式
print("可能的二进制表示:")

# 1. 网络字节序 (大端序)
ip_network = struct.pack('>BBBB', *ip_parts)
print(f"网络字节序 (大端): {ip_network.hex().upper()}")

# 2. 主机字节序 (小端序)  
ip_host = struct.pack('<BBBB', *ip_parts)
print(f"主机字节序 (小端): {ip_host.hex().upper()}")

# 3. 32位整数形式 (大端序)
ip_int_be = struct.pack('>I', (101 << 24) | (126 << 16) | (151 << 8) | 252)
print(f"32位整数 (大端): {ip_int_be.hex().upper()}")

# 4. 32位整数形式 (小端序)
ip_int_le = struct.pack('<I', (101 << 24) | (126 << 16) | (151 << 8) | 252)
print(f"32位整数 (小端): {ip_int_le.hex().upper()}")

# 5. 反向字节序
ip_reversed = struct.pack('BBBB', 252, 151, 126, 101)
print(f"反向字节序: {ip_reversed.hex().upper()}")

print("\n搜索模式:")
patterns = [
    ("网络字节序", "65 7E 97 FC"),
    ("主机字节序", "65 7E 97 FC"), 
    ("32位大端", "657E97FC"),
    ("32位小端", "FC977E65"),
    ("反向字节序", "FC 97 7E 65")
]

for name, pattern in patterns:
    print(f"{name}: {pattern}")

# 计算十进制值
ip_decimal = (101 << 24) | (126 << 16) | (151 << 8) | 252
print(f"\nIP地址的十进制值: {ip_decimal}")
print(f"十六进制: 0x{ip_decimal:08X}")

# 也搜索可能的字符串编码
print(f"\n字符串形式:")
ip_str = "***************"
print(f"ASCII: {ip_str.encode('ascii').hex().upper()}")

# URL编码形式
import urllib.parse
ip_url = urllib.parse.quote(ip_str)
print(f"URL编码: {ip_url}")

print(f"\n建议在IDA中搜索以下十六进制模式:")
print(f"1. 65 7E 97 FC (网络字节序)")
print(f"2. FC 97 7E 65 (小端序)")
print(f"3. {ip_decimal} (十进制)")
print(f"4. 0x{ip_decimal:08X} (十六进制常量)")

# 生成搜索脚本
print(f"\nIDA Python搜索脚本:")
print(f"""
import ida_search
import ida_bytes

# 搜索网络字节序
pattern1 = "65 7E 97 FC"
addr1 = ida_search.find_binary(0, ida_ida.cvar.inf.max_ea, pattern1, 16, ida_search.SEARCH_DOWN)
if addr1 != ida_idaapi.BADADDR:
    print(f"找到网络字节序模式在: 0x{{addr1:X}}")

# 搜索小端序
pattern2 = "FC 97 7E 65" 
addr2 = ida_search.find_binary(0, ida_ida.cvar.inf.max_ea, pattern2, 16, ida_search.SEARCH_DOWN)
if addr2 != ida_idaapi.BADADDR:
    print(f"找到小端序模式在: 0x{{addr2:X}}")
""")
