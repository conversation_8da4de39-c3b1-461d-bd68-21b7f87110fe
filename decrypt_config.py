#!/usr/bin/env python3
"""
解密后门配置数据的脚本
"""

# 从IDA中提取的加密数据
encrypted_data = [
    0x2e, 0x2f, 0x2e, 0x2f, 0x2e, 0x2c, 0x2e, 0x2e, 0x2e, 0x2c, 0x2e, 0x2f, 0x2e, 0x2c, 0x31, 0xbe,
    0x2e, 0x2d, 0x2e, 0x2c, 0x2e, 0x2a, 0x2e, 0x2e, 0xb8, 0x4a, 0x2e, 0x2a, 0x2e, 0x2c, 0x2e, 0x2a,
    0x2e, 0x3b, 0x7b, 0x76, 0x2e, 0x2b, 0x2e, 0x2f, 0x2e, 0x2c, 0x2e, 0x35, 0x2e, 0x29, 0x2e, 0x2d,
    0x2f, 0x2e, 0x1e, 0xaf, 0xb1, 0x1e, 0x23, 0x28, 0x27, 0x4, 0xa8, 0x66, 0xa8, 0xd9, 0x23, 0x2f,
    0x2f, 0x2f, 0x2b, 0x2e, 0x2d, 0xaf, 0xa3, 0x2e, 0x1e, 0xaf, 0xa7, 0x2c, 0xaf, 0xaf, 0x2e, 0xb0,
    0xe5, 0x75, 0xdc, 0xb9, 0xae, 0xc5, 0x31, 0xdd, 0x91, 0x31, 0xae, 0xcc, 0xce, 0x94, 0x1c, 0xf7,
    0xc3, 0x87, 0xb, 0x83, 0xfe, 0x28, 0xe4, 0x84, 0x1d, 0xab, 0xfd, 0xe, 0xe6, 0xea, 0x7d, 0x2d,
    0x7, 0x67, 0x75, 0xee, 0xc8, 0xb1, 0x2a, 0x58, 0x12, 0xcf, 0x87, 0x3d, 0xff, 0xa2, 0xf, 0xbd,
    0x9c, 0xa9, 0xac, 0x3c, 0xbf, 0xd8, 0x60, 0x5e, 0x15, 0x7e, 0x3, 0x59, 0x9a, 0x35, 0x75, 0xde,
    0x46, 0xa3, 0xff, 0x5c, 0x34, 0x12, 0xd2, 0x1f, 0x68, 0x3c, 0xd3, 0x9c, 0xca, 0xd5, 0xb2, 0x6a,
    0x7e, 0x77, 0x68, 0x8d, 0x44, 0x58, 0x51, 0x90, 0x9b, 0x1c, 0xd7, 0x34, 0x15, 0x1a, 0xe9, 0xf0,
    0x1b, 0x1f, 0x6a, 0xdc, 0x27, 0x7f, 0xa, 0x9a, 0xb7, 0x6c, 0xe2, 0xb9, 0x6d, 0x6e, 0x2f, 0xb5,
    0xfc, 0x74, 0xa1, 0x1, 0x1, 0xf5, 0x50, 0x48
]

# XOR密钥
xor_key = 0x2e

print("解密后门配置数据...")
print("=" * 50)

# 解密数据
decrypted_data = []
for byte in encrypted_data:
    decrypted_byte = byte ^ xor_key
    decrypted_data.append(decrypted_byte)

# 将解密后的数据转换为字节串
decrypted_bytes = bytes(decrypted_data)

print("解密后的十六进制数据:")
print(' '.join(f'{b:02x}' for b in decrypted_bytes))
print()

print("解密后的ASCII数据 (可打印字符):")
ascii_str = ""
for b in decrypted_bytes:
    if 32 <= b <= 126:  # 可打印ASCII字符
        ascii_str += chr(b)
    else:
        ascii_str += f"\\x{b:02x}"
print(ascii_str)
print()

# 尝试解析配置结构
print("尝试解析配置结构:")
print("-" * 30)

# 根据beacon函数中的调用，我们知道：
# setting_ptr(8) - hosts
# setting_short(2) - port  
# setting_ptr(9) - ua
# setting_short(1) - ?
# setting_int(3) - sleep_time

# 先分析前面的数据结构
print("前32字节的详细分析:")
for i in range(min(32, len(decrypted_bytes))):
    print(f"偏移 {i:2d}: 0x{decrypted_bytes[i]:02x} ({decrypted_bytes[i]:3d}) {chr(decrypted_bytes[i]) if 32 <= decrypted_bytes[i] <= 126 else '.'}")
print()

# 解析前几个字节作为配置头
offset = 0
while offset < len(decrypted_bytes) - 6:
    try:
        # 读取字段ID (2字节) - 使用小端序
        if offset + 2 > len(decrypted_bytes):
            break
        field_id = decrypted_bytes[offset] | (decrypted_bytes[offset+1] << 8)
        if field_id == 0:
            break

        # 读取类型 (2字节)
        if offset + 4 > len(decrypted_bytes):
            break
        field_type = decrypted_bytes[offset+2] | (decrypted_bytes[offset+3] << 8)

        # 读取长度 (2字节)
        if offset + 6 > len(decrypted_bytes):
            break
        field_length = decrypted_bytes[offset+4] | (decrypted_bytes[offset+5] << 8)
        
        print(f"字段ID: {field_id}, 类型: {field_type}, 长度: {field_length}")
        
        if field_type == 1:  # short
            if offset + 8 <= len(decrypted_bytes):
                value = decrypted_bytes[offset+6] | (decrypted_bytes[offset+7] << 8)
                print(f"  值 (short): {value}")
                if field_id == 2:  # port
                    print(f"  -> 这是端口号: {value}")
                offset += 8
        elif field_type == 2:  # int
            if offset + 10 <= len(decrypted_bytes):
                value = (decrypted_bytes[offset+6] |
                        (decrypted_bytes[offset+7] << 8) |
                        (decrypted_bytes[offset+8] << 16) |
                        (decrypted_bytes[offset+9] << 24))
                print(f"  值 (int): {value}")
                if field_id == 3:  # sleep_time
                    print(f"  -> 这是睡眠时间: {value}ms")
                offset += 10
        elif field_type == 3:  # pointer/string
            if offset + 6 + field_length <= len(decrypted_bytes):
                value_bytes = decrypted_bytes[offset+6:offset+6+field_length]
                try:
                    value_str = value_bytes.decode('utf-8', errors='ignore').rstrip('\x00')
                    print(f"  值 (string): '{value_str}'")
                    if field_id == 8:  # hosts
                        print(f"  -> 这是主机列表: {value_str}")
                    elif field_id == 9:  # user agent
                        print(f"  -> 这是User Agent: {value_str}")
                except:
                    print(f"  值 (hex): {value_bytes.hex()}")
                offset += 6 + field_length
        else:
            print(f"  未知类型: {field_type}")
            break
            
        print()
        
    except Exception as e:
        print(f"解析错误: {e}")
        break

print("\n分析完成!")
