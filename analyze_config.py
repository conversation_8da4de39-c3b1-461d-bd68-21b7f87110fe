#!/usr/bin/env python3
"""
精确分析后门配置数据
"""

# 从IDA中提取的加密数据
encrypted_data = [
    0x2e, 0x2f, 0x2e, 0x2f, 0x2e, 0x2c, 0x2e, 0x2e, 0x2e, 0x2c, 0x2e, 0x2f, 0x2e, 0x2c, 0x31, 0xbe,
    0x2e, 0x2d, 0x2e, 0x2c, 0x2e, 0x2a, 0x2e, 0x2e, 0xb8, 0x4a, 0x2e, 0x2a, 0x2e, 0x2c, 0x2e, 0x2a,
    0x2e, 0x3b, 0x7b, 0x76, 0x2e, 0x2b, 0x2e, 0x2f, 0x2e, 0x2c, 0x2e, 0x35, 0x2e, 0x29, 0x2e, 0x2d,
    0x2f, 0x2e, 0x1e, 0xaf, 0xb1, 0x1e, 0x23, 0x28, 0x27, 0x4, 0xa8, 0x66, 0xa8, 0xd9, 0x23, 0x2f,
    0x2f, 0x2f, 0x2b, 0x2e, 0x2d, 0xaf, 0xa3, 0x2e, 0x1e, 0xaf, 0xa7, 0x2c, 0xaf, 0xaf, 0x2e, 0xb0,
    0xe5, 0x75, 0xdc, 0xb9, 0xae, 0xc5, 0x31, 0xdd, 0x91, 0x31, 0xae, 0xcc, 0xce, 0x94, 0x1c, 0xf7,
    0xc3, 0x87, 0xb, 0x83, 0xfe, 0x28, 0xe4, 0x84, 0x1d, 0xab, 0xfd, 0xe, 0xe6, 0xea, 0x7d, 0x2d,
    0x7, 0x67, 0x75, 0xee, 0xc8, 0xb1, 0x2a, 0x58, 0x12, 0xcf, 0x87, 0x3d, 0xff, 0xa2, 0xf, 0xbd,
    0x9c, 0xa9, 0xac, 0x3c, 0xbf, 0xd8, 0x60, 0x5e, 0x15, 0x7e, 0x3, 0x59, 0x9a, 0x35, 0x75, 0xde,
    0x46, 0xa3, 0xff, 0x5c, 0x34, 0x12, 0xd2, 0x1f, 0x68, 0x3c, 0xd3, 0x9c, 0xca, 0xd5, 0xb2, 0x6a,
    0x7e, 0x77, 0x68, 0x8d, 0x44, 0x58, 0x51, 0x90, 0x9b, 0x1c, 0xd7, 0x34, 0x15, 0x1a, 0xe9, 0xf0,
    0x1b, 0x1f, 0x6a, 0xdc, 0x27, 0x7f, 0xa, 0x9a, 0xb7, 0x6c, 0xe2, 0xb9, 0x6d, 0x6e, 0x2f, 0xb5,
    0xfc, 0x74, 0xa1, 0x1, 0x1, 0xf5, 0x50, 0x48
]

# XOR密钥
xor_key = 0x2e

# 解密数据
decrypted_data = []
for byte in encrypted_data:
    decrypted_byte = byte ^ xor_key
    decrypted_data.append(decrypted_byte)

decrypted_bytes = bytes(decrypted_data)

print("后门配置分析报告")
print("=" * 50)

# 手动解析已知的配置字段
def parse_config():
    offset = 0
    configs = {}
    max_iterations = 20  # 防止无限循环
    iteration = 0

    while offset < len(decrypted_bytes) - 6 and iteration < max_iterations:
        iteration += 1
        print(f"迭代 {iteration}, 偏移 {offset}")

        # 读取字段ID (大端序)
        field_id = (decrypted_bytes[offset] << 8) | decrypted_bytes[offset+1]
        if field_id == 0:
            print("遇到字段ID=0，停止解析")
            break

        # 读取类型 (大端序)
        field_type = (decrypted_bytes[offset+2] << 8) | decrypted_bytes[offset+3]

        # 读取长度 (大端序)
        field_length = (decrypted_bytes[offset+4] << 8) | decrypted_bytes[offset+5]

        print(f"字段 {field_id}: 类型={field_type}, 长度={field_length}")

        # 检查数据是否合理
        if field_length > 1000 or field_type > 10:
            print(f"数据异常，可能解析错误。字段长度={field_length}, 类型={field_type}")
            break
        
        if field_type == 1:  # short
            if offset + 8 <= len(decrypted_bytes):
                value = (decrypted_bytes[offset+6] << 8) | decrypted_bytes[offset+7]
                configs[field_id] = value
                print(f"  值: {value}")

                # 根据beacon函数中的调用分析
                if field_id == 1:
                    print(f"  -> 未知配置1: {value}")
                elif field_id == 2:
                    print(f"  -> 端口号: {value}")
                elif field_id == 5:
                    print(f"  -> Jitter: {value}")

                offset += 8
                print(f"  新偏移: {offset}")

        elif field_type == 2:  # int
            if offset + 10 <= len(decrypted_bytes):
                value = ((decrypted_bytes[offset+6] << 24) |
                        (decrypted_bytes[offset+7] << 16) |
                        (decrypted_bytes[offset+8] << 8) |
                        decrypted_bytes[offset+9])
                configs[field_id] = value
                print(f"  值: {value}")

                if field_id == 3:
                    print(f"  -> 睡眠时间: {value}ms")
                elif field_id == 4:
                    print(f"  -> 缓冲区大小: {value}")

                offset += 10
                print(f"  新偏移: {offset}")
                
        elif field_type == 3:  # string/pointer
            if offset + 6 + field_length <= len(decrypted_bytes):
                value_bytes = decrypted_bytes[offset+6:offset+6+field_length]
                try:
                    # 尝试解码为字符串
                    value_str = value_bytes.decode('utf-8', errors='ignore').rstrip('\x00')
                    if value_str:
                        configs[field_id] = value_str
                        print(f"  值: '{value_str}'")
                        
                        if field_id == 8:
                            print(f"  -> 主机列表: {value_str}")
                            # 分析IP地址
                            if "***************" in value_str:
                                print(f"  *** 发现目标IP地址: *************** ***")
                        elif field_id == 9:
                            print(f"  -> User Agent: {value_str}")
                        elif field_id == 10:
                            print(f"  -> 提交URL: {value_str}")
                        elif field_id == 11:
                            print(f"  -> 恢复程序: {value_str}")
                        elif field_id == 12:
                            print(f"  -> 应用程序: {value_str}")
                    else:
                        # 可能是二进制数据，如RSA密钥
                        print(f"  值 (hex): {value_bytes.hex()}")
                        if field_id == 7 and len(value_bytes) > 100:
                            print(f"  -> RSA公钥数据 (长度: {len(value_bytes)})")
                            
                except Exception as e:
                    print(f"  值 (hex): {value_bytes.hex()}")
                    
                offset += 6 + field_length
                print(f"  新偏移: {offset}")
        else:
            print(f"  未知类型: {field_type}")
            break

        print()

        # 额外的安全检查
        if offset >= len(decrypted_bytes):
            print("到达数据末尾")
            break
    
    return configs

configs = parse_config()

print("\n配置摘要:")
print("-" * 30)
for field_id, value in configs.items():
    print(f"字段 {field_id}: {value}")

# 特别关注网络配置
print("\n网络配置分析:")
print("-" * 30)
if 2 in configs:
    port = configs[2]
    print(f"端口: {port}")
    if port == 8080:
        print("  -> 确认这是HTTP端口8080")

if 8 in configs:
    hosts = configs[8]
    print(f"主机列表: {hosts}")
    if "***************" in str(hosts):
        print("  -> 确认包含目标IP地址 ***************")

if 3 in configs:
    sleep_time = configs[3]
    print(f"心跳间隔: {sleep_time}ms ({sleep_time/1000}秒)")

print("\n威胁分析:")
print("-" * 30)
print("1. 这是一个典型的后门程序配置")
print("2. 程序会连接到 ***************:8080")
print("3. 使用HTTP协议进行通信")
print("4. 具有心跳机制，定期与C&C服务器通信")
print("5. 包含RSA加密功能")
