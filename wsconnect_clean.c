/*
 * 网络连接函数 - wsconnect()
 * 功能: 建立到目标IP和端口的TCP连接
 * 参数: targetip - 目标IP地址字符串 (如 "***************")
 *       port - 目标端口 (如 8080)
 * 返回: 成功返回socket文件描述符，失败返回-1
 */
SOCKET wsconnect(char *targetip, int port)
{
    int socket_fd;
    int socket_option = 1;
    struct timeval timeout;
    struct sockaddr_in server_addr;
    struct hostent *host_entry;
    
    // 1. 创建TCP socket
    socket_fd = socket(AF_INET, SOCK_STREAM, 0);  // IPv4, TCP
    if (socket_fd < 0) {
        perror("socket creation failed");
        return -1;
    }
    
    // 2. 设置socket选项 - 允许地址重用
    if (setsockopt(socket_fd, SOL_SOCKET, SO_REUSEADDR, 
                   (char *)&socket_option, sizeof(socket_option)) != 0) {
        perror("setsockopt failed");
        close(socket_fd);
        return -1;
    }
    
    // 3. 初始化服务器地址结构
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;                    // IPv4
    server_addr.sin_port = htons(port);                  // 网络字节序端口
    
    // 4. 解析IP地址
    if (inet_pton(AF_INET, targetip, &server_addr.sin_addr) <= 0) {
        // 如果不是有效的IP地址，尝试域名解析
        host_entry = gethostbyname(targetip);
        if (!host_entry) {
            perror("address resolution failed");
            close(socket_fd);
            return -1;
        }
        
        // 复制解析得到的IP地址
        memcpy(&server_addr.sin_addr, host_entry->h_addr_list[0], host_entry->h_length);
    }
    
    // 5. 设置连接超时 - 5秒
    timeout.tv_sec = 5;
    timeout.tv_usec = 0;
    
    if (setsockopt(socket_fd, SOL_SOCKET, SO_RCVTIMEO, 
                   (char *)&timeout, sizeof(timeout)) < 0) {
        perror("setsockopt timeout failed");
        close(socket_fd);
        return -1;
    }
    
    // 6. 建立连接
    if (connect(socket_fd, (struct sockaddr *)&server_addr, sizeof(server_addr)) != 0) {
        perror("connection failed");
        close(socket_fd);
        return -1;
    }
    
    // 连接成功，返回socket文件描述符
    return socket_fd;
}

/*
 * 关键点说明:
 * 1. 这个函数负责建立到C&C服务器的TCP连接
 * 2. 支持IP地址和域名两种形式的目标
 * 3. 设置了5秒的连接超时
 * 4. 包含完整的错误处理机制
 * 5. 在strace中看到的connect()系统调用就来自这里
 */
