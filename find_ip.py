#!/usr/bin/env python3
"""
搜索IP地址***************在加密数据中的位置
"""

# 从IDA中提取的加密数据
encrypted_data = [
    0x2e, 0x2f, 0x2e, 0x2f, 0x2e, 0x2c, 0x2e, 0x2e, 0x2e, 0x2c, 0x2e, 0x2f, 0x2e, 0x2c, 0x31, 0xbe,
    0x2e, 0x2d, 0x2e, 0x2c, 0x2e, 0x2a, 0x2e, 0x2e, 0xb8, 0x4a, 0x2e, 0x2a, 0x2e, 0x2c, 0x2e, 0x2a,
    0x2e, 0x3b, 0x7b, 0x76, 0x2e, 0x2b, 0x2e, 0x2f, 0x2e, 0x2c, 0x2e, 0x35, 0x2e, 0x29, 0x2e, 0x2d,
    0x2f, 0x2e, 0x1e, 0xaf, 0xb1, 0x1e, 0x23, 0x28, 0x27, 0x4, 0xa8, 0x66, 0xa8, 0xd9, 0x23, 0x2f,
    0x2f, 0x2f, 0x2b, 0x2e, 0x2d, 0xaf, 0xa3, 0x2e, 0x1e, 0xaf, 0xa7, 0x2c, 0xaf, 0xaf, 0x2e, 0xb0,
    0xe5, 0x75, 0xdc, 0xb9, 0xae, 0xc5, 0x31, 0xdd, 0x91, 0x31, 0xae, 0xcc, 0xce, 0x94, 0x1c, 0xf7,
    0xc3, 0x87, 0xb, 0x83, 0xfe, 0x28, 0xe4, 0x84, 0x1d, 0xab, 0xfd, 0xe, 0xe6, 0xea, 0x7d, 0x2d,
    0x7, 0x67, 0x75, 0xee, 0xc8, 0xb1, 0x2a, 0x58, 0x12, 0xcf, 0x87, 0x3d, 0xff, 0xa2, 0xf, 0xbd,
    0x9c, 0xa9, 0xac, 0x3c, 0xbf, 0xd8, 0x60, 0x5e, 0x15, 0x7e, 0x3, 0x59, 0x9a, 0x35, 0x75, 0xde,
    0x46, 0xa3, 0xff, 0x5c, 0x34, 0x12, 0xd2, 0x1f, 0x68, 0x3c, 0xd3, 0x9c, 0xca, 0xd5, 0xb2, 0x6a,
    0x7e, 0x77, 0x68, 0x8d, 0x44, 0x58, 0x51, 0x90, 0x9b, 0x1c, 0xd7, 0x34, 0x15, 0x1a, 0xe9, 0xf0,
    0x1b, 0x1f, 0x6a, 0xdc, 0x27, 0x7f, 0xa, 0x9a, 0xb7, 0x6c, 0xe2, 0xb9, 0x6d, 0x6e, 0x2f, 0xb5,
    0xfc, 0x74, 0xa1, 0x1, 0x1, 0xf5, 0x50, 0x48
]

# XOR密钥
xor_key = 0x2e

# 解密数据
decrypted_data = []
for byte in encrypted_data:
    decrypted_byte = byte ^ xor_key
    decrypted_data.append(decrypted_byte)

decrypted_bytes = bytes(decrypted_data)

print("搜索IP地址***************")
print("=" * 40)

# IP地址的十六进制表示
# 101 = 0x65, 126 = 0x7E, 151 = 0x97, 252 = 0xFC
ip_pattern = [0x65, 0x7E, 0x97, 0xFC]
ip_pattern_str = "65 7E 97 FC"

print(f"IP地址的十六进制模式: {ip_pattern_str}")
print()

# 搜索模式
found = False
for i in range(len(decrypted_bytes) - 3):
    if (decrypted_bytes[i] == ip_pattern[0] and 
        decrypted_bytes[i+1] == ip_pattern[1] and 
        decrypted_bytes[i+2] == ip_pattern[2] and 
        decrypted_bytes[i+3] == ip_pattern[3]):
        print(f"找到IP地址模式在偏移 {i}")
        print(f"上下文: {decrypted_bytes[max(0,i-10):i+14].hex()}")
        found = True

if not found:
    print("未找到IP地址的十六进制模式")

# 也搜索字符串形式
ip_str = "***************"
ip_bytes = ip_str.encode('ascii')

print(f"\n搜索字符串形式: {ip_str}")
if ip_bytes in decrypted_bytes:
    pos = decrypted_bytes.find(ip_bytes)
    print(f"找到IP地址字符串在偏移 {pos}")
else:
    print("未找到IP地址字符串")

# 搜索各个数字部分
print("\n搜索IP地址的各个部分:")
parts = ["101", "126", "151", "252"]
for part in parts:
    part_bytes = part.encode('ascii')
    if part_bytes in decrypted_bytes:
        pos = decrypted_bytes.find(part_bytes)
        print(f"找到 '{part}' 在偏移 {pos}")

# 显示解密后的可打印字符
print("\n解密后的可打印字符串:")
printable_chars = ""
for i, b in enumerate(decrypted_bytes):
    if 32 <= b <= 126:  # 可打印ASCII字符
        printable_chars += chr(b)
    else:
        if printable_chars:
            print(f"偏移 {i-len(printable_chars)}: '{printable_chars}'")
            printable_chars = ""

if printable_chars:
    print(f"偏移 {len(decrypted_bytes)-len(printable_chars)}: '{printable_chars}'")

print("\n完整的十六进制数据:")
print(' '.join(f'{b:02x}' for b in decrypted_bytes))
